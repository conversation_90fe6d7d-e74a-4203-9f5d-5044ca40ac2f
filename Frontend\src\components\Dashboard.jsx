import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Users,
  BookOpen,
  DollarSign,
  TrendingUp,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  Clock,
  AlertCircle,
  CheckCircle,
  Wallet,
  BarChart3,
  FileText,
  Calculator,
  Settings,
} from "lucide-react";
import Layout from "./shared/Layout";
import SearchBar from "./shared/SearchBar.jsx";
import Badge from "./shared/Badge.jsx";
import LoadingSkeleton from "./shared/LoadingSkeleton.jsx";
import { useToast } from "./shared/Toast.jsx";
import Button from "./shared/Button.jsx";

const Dashboard = () => {
  const toast = useToast();
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  const handleAddTransaction = () => {
    toast.show({ type: "info", message: "د نوې معاملې فورم خلاصیږي..." });
    navigate("/daybook");
  };

  const handleAddCustomer = () => {
    toast.show({ type: "info", message: "د نوي پیرودونکي فورم خلاصیږي..." });
    navigate("/customers");
  };

  const handleViewAll = (section) => {
    toast.show({
      type: "info",
      message: `د ${section} بشپړ لیست ښودل کیږي...`,
    });
  };

  // د آمار کارتونه
  const statsCards = [
    {
      title: "ټول پیرودونکي",
      value: "۱۲۳",
      icon: Users,
      color: "bg-blue-500",
      change: "+۵%",
      changeType: "positive",
      subtitle: "د تیرې میاشتې څخه",
    },
    {
      title: "د نن ورځې راکړه ورکړه",
      value: "۴۵,۰۰۰",
      currency: "افغانۍ",
      icon: DollarSign,
      color: "bg-green-500",
      change: "+۱۲%",
      changeType: "positive",
      subtitle: "د پرون څخه",
    },
    {
      title: "د میاشتې راکړه ورکړه",
      value: "۱,۲۳۴,۵۶۷",
      currency: "افغانۍ",
      icon: TrendingUp,
      color: "bg-purple-500",
      change: "+۸%",
      changeType: "positive",
      subtitle: "د تیرې میاشتې څخه",
    },
    {
      title: "د نن ورځې کتابونه",
      value: "۷",
      icon: BookOpen,
      color: "bg-orange-500",
      change: "+۳",
      changeType: "positive",
      subtitle: "نوي کتابونه",
    },

    {
      title: "د میزان توپیر",
      value: "۲۳,۴۵۶",
      currency: "افغانۍ",
      icon: Wallet,
      color: "bg-red-500",
      change: "-۱.۵%",
      changeType: "negative",
      subtitle: "د نن ورځې",
    },
  ];

  // د منیو آیټمونه
  const menuItems = [
    {
      name: "کورپاڼه",
      icon: TrendingUp,
      path: "/dashboard",
      active: true,
      badge: null,
    },
    { name: "پیرودونکي", icon: Users, path: "/customers", badge: "۱۲۳" },
    { name: "ورځني کتاب", icon: BookOpen, path: "/daybook", badge: "۷" },
    { name: "د پیرودونکي کاتا", icon: FileText, path: "/ledger", badge: null },
    { name: "راپورونه", icon: BarChart3, path: "/reports", badge: null },
    { name: "حسابات", icon: Calculator, path: "/accounts", badge: null },
    ...(user?.role === "super_admin"
      ? [{ name: "د اډمین پینل", icon: Settings, path: "/admin", badge: null }]
      : []),
    { name: "تنظیمات", icon: Settings, path: "/settings", badge: null },
  ];

  // د اخیرو فعالیتونو ډیټا
  const recentTransactions = [
    {
      id: 1,
      customerName: "احمد علي",
      type: "جمعه",
      amount: "۵,۰۰۰",
      currency: "افغانۍ",
      time: "۱۰:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 2,
      customerName: "فاطمه خان",
      type: "نام",
      amount: "۲,۵۰۰",
      currency: "افغانۍ",
      time: "۱۰:۱۵ صبح",
      status: "بشپړ",
    },
    {
      id: 3,
      customerName: "محمد حسن",
      type: "جمعه",
      amount: "۱۰,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۴۵ صبح",
      status: "بشپړ",
    },
    {
      id: 4,
      customerName: "عایشه احمد",
      type: "نام",
      amount: "۷,۵۰۰",
      currency: "افغانۍ",
      time: "۹:۳۰ صبح",
      status: "بشپړ",
    },
    {
      id: 5,
      customerName: "علي رضا",
      type: "جمعه",
      amount: "۳,۰۰۰",
      currency: "افغانۍ",
      time: "۹:۱۵ صبح",
      status: "بشپړ",
    },
  ];

  // د نویو پیرودونکو ډیټا
  const newCustomers = [
    {
      id: 1,
      name: "محمد حسن",
      phone: "۰۷۹۹۱۲۳۴۵۶",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 2,
      name: "فریده احمد",
      phone: "۰۷۰۰۹۸۷۶۵۴",
      registrationTime: "د نن ورځې",
      status: "فعال",
    },
    {
      id: 3,
      name: "احمد شاه",
      phone: "۰۷۸۸۵۵۵۱۲۳",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 4,
      name: "مریم خان",
      phone: "۰۷۷۷۴۴۴۳۲۱",
      registrationTime: "پرون",
      status: "فعال",
    },
    {
      id: 5,
      name: "عبدالله رحیم",
      phone: "۰۷۶۶۳۳۳۲۱۰",
      registrationTime: "۲ ورځې دمخه",
      status: "فعال",
    },
  ];

  return (
    <Layout title='کورپاڼه'>
      <div className='space-y-6'>
        {/* Header with search and actions */}
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
              کورپاڼه
            </h1>
            <p className='text-gray-600 pashto-text'>د نن ورځې لنډیز</p>
          </div>
          <div className='flex space-x-3'>
            <Button onClick={handleAddCustomer} variant='secondary'>
              <Plus className='h-4 w-4 ml-2' />
              نوی پیرودونکی
            </Button>
            <Button onClick={handleAddTransaction} variant='primary'>
              <Plus className='h-4 w-4 ml-2' />
              نوې معامله
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        <div className='bg-white p-4 rounded-lg shadow-md'>
          <SearchBar
            value={searchTerm}
            onChange={setSearchTerm}
            placeholder='د پیرودونکي، معاملې یا حساب لټون...'
            onClear={() => setSearchTerm("")}
          />
        </div>

        {/* Stats Cards */}
        {loading ? (
          <LoadingSkeleton type='stats' />
        ) : (
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
            {statsCards.map((card, index) => (
              <div key={index} className='bg-white rounded-lg shadow-md p-6'>
                <div className='flex items-center'>
                  <div
                    className={`w-12 h-12 ${card.color} rounded-lg flex items-center justify-center ml-4`}
                  >
                    <card.icon className='h-6 w-6 text-white' />
                  </div>
                  <div className='flex-1'>
                    <p className='text-sm font-medium text-gray-600 pashto-text'>
                      {card.title}
                    </p>
                    <div className='flex items-center'>
                      <p className='text-2xl font-bold text-gray-900'>
                        {card.value}{" "}
                        {card.currency && (
                          <span className='text-sm text-gray-500'>
                            {card.currency}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className='flex items-center mt-1'>
                      {card.changeType === "positive" ? (
                        <ArrowUpRight className='h-4 w-4 text-green-500 ml-1' />
                      ) : (
                        <ArrowDownRight className='h-4 w-4 text-red-500 ml-1' />
                      )}
                      <span
                        className={`text-sm ${
                          card.changeType === "positive"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {card.change}
                      </span>
                      <span className='text-sm text-gray-500 mr-2 pashto-text'>
                        {card.subtitle}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Recent Transactions and New Customers */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* Recent Transactions */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='p-6 border-b border-gray-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium text-gray-900 pashto-text'>
                  وروستي معاملات
                </h3>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleViewAll("معاملات")}
                >
                  <Eye className='h-4 w-4 ml-1' />
                  ټول وګورئ
                </Button>
              </div>
            </div>
            <div className='p-6'>
              {loading ? (
                <LoadingSkeleton type='table' rows={3} />
              ) : (
                <div className='space-y-4'>
                  {recentTransactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                    >
                      <div className='flex items-center'>
                        <div
                          className={`w-10 h-10 rounded-lg flex items-center justify-center ml-3 ${
                            transaction.type === "جمعه"
                              ? "bg-green-100"
                              : "bg-blue-100"
                          }`}
                        >
                          {transaction.type === "جمعه" ? (
                            <ArrowUpRight
                              className={`h-5 w-5 ${
                                transaction.type === "جمعه"
                                  ? "text-green-600"
                                  : "text-blue-600"
                              }`}
                            />
                          ) : (
                            <ArrowDownRight className='h-5 w-5 text-blue-600' />
                          )}
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900 pashto-text'>
                            {transaction.customer}
                          </p>
                          <p className='text-xs text-gray-500 pashto-text'>
                            {transaction.time}
                          </p>
                        </div>
                      </div>
                      <div className='text-left'>
                        <Badge
                          variant={
                            transaction.type === "جمعه" ? "success" : "info"
                          }
                        >
                          {transaction.amount} افغانۍ
                        </Badge>
                        <p className='text-xs text-gray-500 mt-1 pashto-text'>
                          {transaction.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* New Customers */}
          <div className='bg-white rounded-lg shadow-md'>
            <div className='p-6 border-b border-gray-200'>
              <div className='flex items-center justify-between'>
                <h3 className='text-lg font-medium text-gray-900 pashto-text'>
                  نوي پیرودونکي
                </h3>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleViewAll("پیرودونکي")}
                >
                  <Eye className='h-4 w-4 ml-1' />
                  ټول وګورئ
                </Button>
              </div>
            </div>
            <div className='p-6'>
              {loading ? (
                <LoadingSkeleton type='table' rows={3} />
              ) : (
                <div className='space-y-4'>
                  {newCustomers.map((customer) => (
                    <div
                      key={customer.id}
                      className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'
                    >
                      <div className='flex items-center'>
                        <div className='w-10 h-10 bg-sarafi-100 rounded-lg flex items-center justify-center ml-3'>
                          <Users className='h-5 w-5 text-sarafi-600' />
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900 pashto-text'>
                            {customer.name}
                          </p>
                          <p className='text-xs text-gray-500 font-mono'>
                            {customer.phone}
                          </p>
                        </div>
                      </div>
                      <div className='text-left'>
                        <Badge variant='success'>{customer.status}</Badge>
                        <p className='text-xs text-gray-500 mt-1 pashto-text'>
                          {customer.registrationTime}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
