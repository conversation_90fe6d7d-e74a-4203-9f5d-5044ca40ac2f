from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>

from .views import UserViewSet, CompanyViewSet, DaybookViewSet, DaybookTransactionViewSet


router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'companies', CompanyViewSet)
router.register(r'daybooks', DaybookViewSet)
router.register(r'daybook-transactions', DaybookTransactionViewSet)

urlpatterns = [
    path('', include(router.urls)),
]