import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff, User, Lock, LogIn } from "lucide-react";

const Login = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // د API د اړیکې لپاره وروسته به دا ځای بدل شي
      await new Promise((resolve) => setTimeout(resolve, 1000));
      if (formData.username === "admin" && formData.password === "admin123") {
        localStorage.setItem("token", "mock-jwt-token");
        localStorage.setItem(
          "user",
          JSON.stringify({
            id: 1,
            username: "admin",
            full_name: "د سیسټم مدیر",
            role: "super_admin",
          })
        );
        navigate("/dashboard");
      } else {
        setError("د کاروونکي نوم یا پاسورډ غلط دی");
      }
    } catch (err) {
      setError("د ننه کیدو کې ستونزه");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-sarafi-500 to-sarafi-700 flex items-center justify-center p-4'>
      <div className='bg-white rounded-2xl shadow-2xl w-full max-w-md p-8'>
        {/* سرلیک */}
        <div className='text-center mb-8'>
          <div className='mx-auto w-16 h-16 bg-sarafi-100 rounded-full flex items-center justify-center mb-4'>
            <LogIn className='w-8 h-8 text-sarafi-600' />
          </div>
          <h1 className='text-2xl font-bold text-gray-900 pashto-text'>
            د صرافۍ سیسټم ته ننه کیدل
          </h1>
          <p className='text-gray-600 mt-2 pashto-text'>
            د خپل حساب معلومات ولیکئ
          </p>
        </div>

        {/* د غلطۍ پیغام */}
        {error && (
          <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6 pashto-text'>
            {error}
          </div>
        )}

        {/* د ننه کیدو فورم */}
        <form onSubmit={handleSubmit} className='space-y-6'>
          {/* د کاروونکي نوم */}
          <div>
            <label
              htmlFor='username'
              className='block text-sm font-medium text-gray-700 mb-2 pashto-text'
            >
              د کاروونکي نوم
            </label>
            <div className='relative'>
              <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
                <User className='h-5 w-5 text-gray-400' />
              </div>
              <input
                type='text'
                id='username'
                name='username'
                value={formData.username}
                onChange={handleChange}
                className='input-field pr-10'
                placeholder='د کاروونکي نوم ولیکئ'
                required
                dir='ltr'
              />
            </div>
          </div>

          {/* پاسورډ */}
          <div>
            <label
              htmlFor='password'
              className='block text-sm font-medium text-gray-700 mb-2 pashto-text'
            >
              پاسورډ
            </label>
            <div className='relative'>
              <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
                <Lock className='h-5 w-5 text-gray-400' />
              </div>
              <input
                type={showPassword ? "text" : "password"}
                id='password'
                name='password'
                value={formData.password}
                onChange={handleChange}
                className='input-field pr-10 pl-10'
                placeholder='پاسورډ ولیکئ'
                required
                dir='ltr'
              />
              <button
                type='button'
                className='absolute inset-y-0 left-0 pl-3 flex items-center'
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className='h-5 w-5 text-gray-400 hover:text-gray-600' />
                ) : (
                  <Eye className='h-5 w-5 text-gray-400 hover:text-gray-600' />
                )}
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type='submit'
            disabled={loading}
            className='w-full btn-primary py-3 text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed pashto-text'
          >
            {loading ? (
              <div className='flex items-center justify-center'>
                <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2'></div>
                د ننه کیدو پروسه...
              </div>
            ) : (
              "ننه کیدل"
            )}
          </button>
        </form>

        {/* د ټیسټ معلومات */}
        <div className='mt-6 p-4 bg-gray-50 rounded-lg'>
          <p className='text-sm text-gray-600 pashto-text mb-2'>
            د ټیسټ لپاره:
          </p>
          <p className='text-xs text-gray-500 pashto-text'>
            کاروونکي نوم: admin
          </p>
          <p className='text-xs text-gray-500 pashto-text'>پاسورډ: admin123</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
