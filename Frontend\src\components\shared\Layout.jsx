import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Users,
  BookOpen,
  TrendingUp,
  Menu,
  X,
  LogOut,
  Settings,
  FileText,
  Calculator,
  Bell,
  Search,
  Calendar,
  Clock,
  Banknote,
  BarChart3,
} from "lucide-react";

const Layout = ({ children, title }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    const token = localStorage.getItem("token");

    if (!userData || !token) {
      navigate("/login");
      return;
    }

    setUser(JSON.parse(userData));
  }, [navigate]);

  const handleLogout = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    navigate("/login");
  };

  const menuItems = [
    { name: "کورپاڼه", icon: TrendingUp, path: "/dashboard", badge: null },
    { name: "پیرودونکي", icon: Users, path: "/customers", badge: "۱۲۳" },
    { name: "ورځني کتاب", icon: BookOpen, path: "/daybook", badge: "۷" },
    { name: "د پیرودونکي کاتا", icon: FileText, path: "/ledger", badge: null },
    { name: "اسعار", icon: Banknote, path: "/exchange-rates", badge: null },
    { name: "راپورونه", icon: BarChart3, path: "/reports", badge: null },
    { name: "حسابات", icon: Calculator, path: "/accounts", badge: null },
    ...(user?.role === "super_admin"
      ? [{ name: "د اډمین پینل", icon: Settings, path: "/admin", badge: null }]
      : []),
    { name: "تنظیمات", icon: Settings, path: "/settings", badge: null },
  ];

  if (!user) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 flex'>
      {/* د اړخ منیو */}
      <div
        className={`fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform ${
          sidebarOpen ? "translate-x-0" : "translate-x-full"
        } transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}
      >
        <div className='flex items-center justify-between h-16 px-6 border-b border-gray-200'>
          <h1 className='text-xl font-bold text-gray-900 pashto-text'>
            د صرافۍ سیسټم
          </h1>
          <button onClick={() => setSidebarOpen(false)} className='lg:hidden'>
            <X className='h-6 w-6 text-gray-500' />
          </button>
        </div>

        <nav className='mt-6'>
          {menuItems.map((item) => (
            <a
              key={item.name}
              href={item.path}
              onClick={(e) => {
                e.preventDefault();
                navigate(item.path);
                setSidebarOpen(false);
              }}
              className={`flex items-center justify-between px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                location.pathname === item.path
                  ? "bg-sarafi-50 text-sarafi-700 border-l-4 border-sarafi-700"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              }`}
            >
              <div className='flex items-center'>
                <item.icon className='ml-3 h-5 w-5' />
                <span className='pashto-text'>{item.name}</span>
              </div>
              {item.badge && (
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${
                    item.badge === "نوی"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {item.badge}
                </span>
              )}
            </a>
          ))}
        </nav>

        {/* د کاروونکي معلومات */}
        <div className='absolute bottom-0 w-full p-6 border-t border-gray-200'>
          <div className='flex items-center'>
            <div className='flex-shrink-0'>
              <div className='h-10 w-10 bg-sarafi-100 rounded-full flex items-center justify-center'>
                <span className='text-sarafi-600 font-medium'>
                  {user.full_name?.charAt(0) || "م"}
                </span>
              </div>
            </div>
            <div className='mr-3'>
              <p className='text-sm font-medium text-gray-900 pashto-text'>
                {user.full_name}
              </p>
              <p className='text-xs text-gray-500 pashto-text'>
                {user.role === "super_admin" ? "لوی مدیر" : "کاروونکی"}
              </p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className='mt-3 w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 transition-colors duration-200'
          >
            <LogOut className='ml-2 h-4 w-4' />
            <span className='pashto-text'>وتل</span>
          </button>
        </div>
      </div>

      {/* اصلي مینه */}
      <div className='flex-1'>
        {/* د پورته برخه */}
        <header className='bg-white shadow-sm border-b border-gray-200'>
          <div className='flex items-center justify-between h-16 px-6'>
            <div className='flex items-center'>
              <button
                onClick={() => setSidebarOpen(true)}
                className='lg:hidden ml-4'
              >
                <Menu className='h-6 w-6 text-gray-500' />
              </button>
              <h2 className='text-2xl font-bold text-gray-900 pashto-text'>
                {title || "کورپاڼه"}
              </h2>
            </div>

            <div className='flex items-center space-x-4'>
              {/* د لټون برخه */}
              <div className='relative hidden md:block'>
                <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
                  <Search className='h-5 w-5 text-gray-400' />
                </div>
                <input
                  type='text'
                  placeholder='د پیرودونکي لټون...'
                  className='block w-64 pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-sarafi-500 focus:border-sarafi-500 text-sm'
                  dir='rtl'
                />
              </div>

              {/* د اطلاعاتو برخه */}
              <button className='relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sarafi-500'>
                <Bell className='h-6 w-6' />
                <span className='absolute top-0 left-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white'></span>
              </button>

              {/* د نیټې ښودنه */}
              <div className='flex items-center text-sm text-gray-500 pashto-text'>
                <Calendar className='h-4 w-4 ml-2' />
                <span>نن: {new Date().toLocaleDateString("fa-AF")}</span>
              </div>

              {/* د وخت ښودنه */}
              <div className='flex items-center text-sm text-gray-500 pashto-text'>
                <Clock className='h-4 w-4 ml-2' />
                <span>
                  {new Date().toLocaleTimeString("fa-AF", {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* د مینې مینه */}
        <main className='p-6'>{children}</main>
      </div>

      {/* د اړخ منیو د تیاره کولو لپاره */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden'
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default Layout;
