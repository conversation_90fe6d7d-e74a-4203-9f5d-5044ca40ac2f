import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import Login from "./components/Login";
import Dashboard from "./components/Dashboard";
import Customers from "./components/Customers";
import Daybook from "./components/Daybook";
import Ledger from "./components/Ledger";
import Reports from "./components/Reports";
import Accounts from "./components/Accounts";
import Settings from "./components/Settings";
import AdminPanel from "./components/AdminPanel";
import ExchangeRates from "./components/ExchangeRates";

// د محافظت شوي روټ کمپوننټ
const ProtectedRoute = ({ children }) => {
  const token = localStorage.getItem("token");
  return token ? children : <Navigate to='/login' replace />;
};

const NotFound = () => (
  <div className='min-h-screen flex items-center justify-center bg-gray-50'>
    <div className='text-center'>
      <h1 className='text-3xl font-bold text-gray-900 pashto-text mb-2'>
        پاڼه و نه موندل شوه
      </h1>
      <p className='text-gray-600 pashto-text mb-6'>
        هغه پاڼه چې تاسو ورته ځئ شتون نه لري
      </p>
      <a href='/dashboard' className='btn-primary inline-block'>
        کورپاڼې ته تلل
      </a>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <div className='App'>
        <Routes>
          <Route path='/login' element={<Login />} />
          <Route
            path='/dashboard'
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path='/'
            element={
              <ProtectedRoute>
                <Navigate to='/dashboard' replace />
              </ProtectedRoute>
            }
          />
          {/* د نورو صفحو لپاره روټونه دلته اضافه کړئ */}
          <Route
            path='/customers'
            element={
              <ProtectedRoute>
                <Customers />
              </ProtectedRoute>
            }
          />
          <Route
            path='/daybook'
            element={
              <ProtectedRoute>
                <Daybook />
              </ProtectedRoute>
            }
          />
          <Route
            path='/ledger'
            element={
              <ProtectedRoute>
                <Ledger />
              </ProtectedRoute>
            }
          />
          <Route
            path='/exchange-rates'
            element={
              <ProtectedRoute>
                <ExchangeRates />
              </ProtectedRoute>
            }
          />
          <Route
            path='/reports'
            element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            }
          />
          <Route
            path='/accounts'
            element={
              <ProtectedRoute>
                <Accounts />
              </ProtectedRoute>
            }
          />
          <Route
            path='/settings'
            element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            }
          />
          <Route
            path='/admin'
            element={
              <ProtectedRoute>
                <AdminPanel />
              </ProtectedRoute>
            }
          />
          <Route path='*' element={<NotFound />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
