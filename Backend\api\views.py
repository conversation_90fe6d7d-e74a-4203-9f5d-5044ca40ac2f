from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from core.models import User, Company, Daybook, DaybookTransaction
from .serializers import (
    UserSerializer, CompanySerializer, DaybookSerializer,
    DaybookListSerializer, DaybookTransactionSerializer
)

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]

class DaybookViewSet(viewsets.ModelViewSet):
    queryset = Daybook.objects.all()
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'list':
            return DaybookListSerializer
        return DaybookSerializer

    def get_queryset(self):
        """All users can see all daybooks, but editing is restricted"""
        return Daybook.objects.all().select_related('created_by').prefetch_related('transactions')

    def perform_create(self, serializer):
        """Set the current user as the creator"""
        serializer.save(created_by=self.request.user)

    def update(self, request, *args, **kwargs):
        """Only allow editing if user has permission"""
        daybook = self.get_object()

        # Check if user can edit this daybook
        if not self._can_edit_daybook(daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        """Only allow editing if user has permission"""
        daybook = self.get_object()

        # Check if user can edit this daybook
        if not self._can_edit_daybook(daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """Only allow deletion if user has permission"""
        daybook = self.get_object()

        # Check if user can edit this daybook
        if not self._can_edit_daybook(daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د ړنګولو اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().destroy(request, *args, **kwargs)

    def _can_edit_daybook(self, daybook, user):
        """Check if user can edit the daybook"""
        # Super admin and admin can edit all daybooks
        if user.role in ['super_admin', 'admin']:
            return True

        # Creator can edit their own daybook
        return daybook.created_by == user

    @action(detail=False, methods=['get'])
    def my_daybooks(self, request):
        """Get daybooks created by current user"""
        daybooks = self.get_queryset().filter(created_by=request.user)
        serializer = self.get_serializer(daybooks, many=True)
        return Response(serializer.data)

class DaybookTransactionViewSet(viewsets.ModelViewSet):
    queryset = DaybookTransaction.objects.all()
    serializer_class = DaybookTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter transactions by daybook if specified"""
        queryset = DaybookTransaction.objects.all().select_related('daybook', 'daybook__created_by')
        daybook_id = self.request.query_params.get('daybook', None)
        if daybook_id is not None:
            queryset = queryset.filter(daybook_id=daybook_id)
        return queryset

    def create(self, request, *args, **kwargs):
        """Only allow creating transactions if user can edit the daybook"""
        daybook_id = request.data.get('daybook')
        if daybook_id:
            try:
                daybook = Daybook.objects.get(id=daybook_id)
                if not self._can_edit_daybook(daybook, request.user):
                    return Response(
                        {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                        status=status.HTTP_403_FORBIDDEN
                    )
            except Daybook.DoesNotExist:
                return Response(
                    {'error': 'ورځني کتاب موندل نشو'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        """Only allow updating transactions if user can edit the daybook"""
        transaction = self.get_object()
        if not self._can_edit_daybook(transaction.daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        """Only allow updating transactions if user can edit the daybook"""
        transaction = self.get_object()
        if not self._can_edit_daybook(transaction.daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().partial_update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        """Only allow deleting transactions if user can edit the daybook"""
        transaction = self.get_object()
        if not self._can_edit_daybook(transaction.daybook, request.user):
            return Response(
                {'error': 'تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ'},
                status=status.HTTP_403_FORBIDDEN
            )

        return super().destroy(request, *args, **kwargs)

    def _can_edit_daybook(self, daybook, user):
        """Check if user can edit the daybook"""
        # Super admin and admin can edit all daybooks
        if user.role in ['super_admin', 'admin']:
            return True

        # Creator can edit their own daybook
        return daybook.created_by == user