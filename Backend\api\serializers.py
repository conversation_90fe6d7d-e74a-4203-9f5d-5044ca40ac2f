from rest_framework import serializers
from core.models import User, Company, Daybook, DaybookTransaction

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'username', 'full_name', 'email', 'phone', 'role', 'status', 'created_at', 'updated_at')
        read_only_fields = ('created_at', 'updated_at')

class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class DaybookTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = DaybookTransaction
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class DaybookSerializer(serializers.ModelSerializer):
    transactions = DaybookTransactionSerializer(many=True, read_only=True)
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    can_edit = serializers.SerializerMethodField()

    class Meta:
        model = Daybook
        fields = '__all__'
        read_only_fields = ('balance', 'created_at', 'updated_at')

    def get_can_edit(self, obj):
        """Check if current user can edit this daybook"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False

        # Super admin and admin can edit all daybooks
        if request.user.role in ['super_admin', 'admin']:
            return True

        # Creator can edit their own daybook
        return obj.created_by == request.user

class DaybookListSerializer(serializers.ModelSerializer):
    """Simplified serializer for listing daybooks"""
    created_by_name = serializers.CharField(source='created_by.full_name', read_only=True)
    can_edit = serializers.SerializerMethodField()
    transaction_count = serializers.IntegerField(source='transactions.count', read_only=True)

    class Meta:
        model = Daybook
        fields = ('id', 'page_number', 'date', 'status', 'total_jamah', 'total_naam',
                 'balance', 'created_by_name', 'can_edit', 'transaction_count', 'created_at')
        read_only_fields = ('balance', 'created_at')

    def get_can_edit(self, obj):
        """Check if current user can edit this daybook"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False

        # Super admin and admin can edit all daybooks
        if request.user.role in ['super_admin', 'admin']:
            return True

        # Creator can edit their own daybook
        return obj.created_by == request.user