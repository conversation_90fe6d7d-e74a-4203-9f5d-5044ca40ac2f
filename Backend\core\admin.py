from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User, Company, Daybook, DaybookTransaction

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('username', 'full_name', 'email', 'role', 'status', 'created_at')
    list_filter = ('role', 'status', 'is_active', 'created_at')
    search_fields = ('username', 'full_name', 'email')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {
            'fields': ('full_name', 'phone', 'role', 'status', 'created_at', 'updated_at')
        }),
    )

    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Additional Info', {
            'fields': ('full_name', 'phone', 'role', 'status')
        }),
    )

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('company_name', 'owner_name', 'mobile_number', 'created_at')
    search_fields = ('company_name', 'owner_name')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Daybook)
class DaybookAdmin(admin.ModelAdmin):
    list_display = ('page_number', 'date', 'created_by', 'status', 'total_jamah', 'total_naam', 'balance', 'created_at')
    list_filter = ('status', 'date', 'created_by')
    search_fields = ('page_number', 'created_by__full_name', 'created_by__username')
    readonly_fields = ('balance', 'created_at', 'updated_at')
    date_hierarchy = 'date'

@admin.register(DaybookTransaction)
class DaybookTransactionAdmin(admin.ModelAdmin):
    list_display = ('daybook', 'index', 'customer_name', 'transaction_type', 'amount', 'currency', 'time')
    list_filter = ('transaction_type', 'currency', 'daybook__date')
    search_fields = ('customer_name', 'description', 'daybook__page_number')
    readonly_fields = ('created_at', 'updated_at')