from django.db import models
from django.utils import timezone
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    """Custom User model with additional fields"""
    full_name = models.CharField(max_length=100)
    phone = models.CharField(max_length=15, blank=True, null=True)
    role = models.CharField(max_length=20, choices=[
        ('super_admin', 'Super Admin'),
        ('admin', 'Admin'),
        ('manager', 'Manager'),
        ('cashier', 'Cashier'),
        ('accountant', 'Accountant'),
    ], default='cashier')
    status = models.CharField(max_length=10, choices=[
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ], default='active')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.full_name} ({self.username})"

    class Meta:
        verbose_name_plural = "Users"
        ordering = ['-created_at']

class Company(models.Model):
    id = models.AutoField(primary_key=True)
    owner_name = models.CharField(max_length=100)
    company_name = models.CharField(max_length=100)
    mobile_number = models.CharField(max_length=15)
    logo = models.ImageField(upload_to='company_logos/', blank=True, null=True)
    address = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name

    class Meta:
        verbose_name_plural = "Companies"
        ordering = ['-created_at']

class Daybook(models.Model):
    """Daybook model for daily transaction records"""
    STATUS_CHOICES = [
        ('open', 'خلاص'),  # Open
        ('closed', 'تړل شوی'),  # Closed
    ]

    id = models.AutoField(primary_key=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='daybooks')
    date = models.DateField(default=timezone.now)
    page_number = models.PositiveIntegerField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='open')
    total_jamah = models.DecimalField(max_digits=15, decimal_places=2, default=0)  # Total credit
    total_naam = models.DecimalField(max_digits=15, decimal_places=2, default=0)   # Total debit
    balance = models.DecimalField(max_digits=15, decimal_places=2, default=0)      # Balance
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Daybook {self.page_number} - {self.date} by {self.created_by.full_name}"

    def save(self, *args, **kwargs):
        # Auto-calculate balance
        self.balance = self.total_jamah - self.total_naam
        super().save(*args, **kwargs)

    class Meta:
        verbose_name_plural = "Daybooks"
        ordering = ['-date', '-page_number']
        unique_together = ['date', 'page_number']

class DaybookTransaction(models.Model):
    """Individual transactions within a daybook"""
    TRANSACTION_TYPES = [
        ('jamah', 'جمعه'),  # Credit
        ('naam', 'نام'),   # Debit
    ]

    CURRENCY_CHOICES = [
        ('AFN', 'افغانۍ'),
        ('USD', 'ډالر'),
        ('EUR', 'یورو'),
        ('PKR', 'پاکستاني روپۍ'),
    ]

    id = models.AutoField(primary_key=True)
    daybook = models.ForeignKey(Daybook, on_delete=models.CASCADE, related_name='transactions')
    index = models.PositiveIntegerField()  # Order within the daybook
    customer_name = models.CharField(max_length=100)
    transaction_type = models.CharField(max_length=10, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='AFN')
    description = models.TextField()
    time = models.TimeField()
    balance_after = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.customer_name} - {self.transaction_type} - {self.amount} {self.currency}"

    class Meta:
        verbose_name_plural = "Daybook Transactions"
        ordering = ['daybook', 'index']
        unique_together = ['daybook', 'index']